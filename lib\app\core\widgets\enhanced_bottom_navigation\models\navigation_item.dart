import 'package:flutter/material.dart';

/// Enhanced bottom navigation item model
class EnhancedBottomNavigationItem {
  final IconData icon;
  final IconData? selectedIcon;
  final IconData? activeIcon;
  final String label;
  final Color? backgroundColor;
  final Widget? badge;
  final int? badgeCount;
  final String? tooltip;

  const EnhancedBottomNavigationItem({
    required this.icon,
    required this.label,
    this.selectedIcon,
    this.activeIcon,
    this.backgroundColor,
    this.badge,
    this.badgeCount,
    this.tooltip,
  });

  /// Get the active icon, preferring selectedIcon over activeIcon
  IconData get effectiveActiveIcon => selectedIcon ?? activeIcon ?? icon;
}
