import 'package:flutter/material.dart';
import '../../../core/utils/responsive_utils.dart';

/// Responsive bottom navigation bar
class ResponsiveBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;
  final List<BottomNavigationBarItem> items;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double? elevation;

  const ResponsiveBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final responsiveIconSize = ResponsiveUtils.responsive(
      context,
      mobile: 24.0,
      tablet: 26.0,
      desktop: 28.0,
    );

    final responsiveSelectedFontSize = ResponsiveUtils.responsive(
      context,
      mobile: 12.0,
      tablet: 13.0,
      desktop: 14.0,
    );

    final responsiveUnselectedFontSize = ResponsiveUtils.responsive(
      context,
      mobile: 10.0,
      tablet: 11.0,
      desktop: 12.0,
    );

    return BottomNavigationBar(
      currentIndex: currentIndex,
      onTap: onTap,
      items: items,
      backgroundColor: backgroundColor,
      selectedItemColor: selectedItemColor ?? theme.colorScheme.primary,
      unselectedItemColor: unselectedItemColor ?? 
          theme.colorScheme.onSurface.withValues(alpha: 0.6),
      elevation: elevation ?? 8,
      iconSize: responsiveIconSize,
      selectedFontSize: responsiveSelectedFontSize,
      unselectedFontSize: responsiveUnselectedFontSize,
      type: items.length > 3 
          ? BottomNavigationBarType.fixed 
          : BottomNavigationBarType.shifting,
    );
  }
}
