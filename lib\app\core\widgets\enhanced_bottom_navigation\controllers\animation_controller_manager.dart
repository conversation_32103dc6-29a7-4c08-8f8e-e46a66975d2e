import 'package:flutter/material.dart';

/// Manages all animation controllers for enhanced bottom navigation
class AnimationControllerManager {
  late final AnimationController indicatorController;
  late final AnimationController iconController;
  late final AnimationController rippleController;
  late final AnimationController floatingController;

  late final Animation<double> indicatorSlideAnimation;
  late final Animation<double> iconBounceAnimation;
  late final Animation<double> rippleAnimation;
  late final Animation<double> floatingAnimation;

  void initialize(TickerProvider vsync) {
    indicatorController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 400),
    );

    iconController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 300),
    );

    rippleController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 600),
    );

    floatingController = AnimationController(
      vsync: vsync,
      duration: const Duration(milliseconds: 2000),
    );

    indicatorSlideAnimation = CurvedAnimation(
      parent: indicatorController,
      curve: Curves.easeInOutCubic,
    );

    iconBounceAnimation = CurvedAnimation(
      parent: iconController,
      curve: Curves.elasticOut,
    );

    rippleAnimation = CurvedAnimation(
      parent: rippleController,
      curve: Curves.easeOut,
    );

    floatingAnimation = CurvedAnimation(
      parent: floatingController,
      curve: Curves.easeInOut,
    );

    // Start floating animation
    floatingController.repeat(reverse: true);
  }

  void dispose() {
    indicatorController.dispose();
    iconController.dispose();
    rippleController.dispose();
    floatingController.dispose();
  }

  void animateToIndex(int index) {
    indicatorController.forward();
    iconController.forward().then((_) {
      iconController.reverse();
    });
  }

  void triggerRipple() {
    rippleController.forward().then((_) {
      rippleController.reset();
    });
  }
}
