import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/storage_service.dart';
import '../../../routes/app_routes.dart';

class PrivacySettingsController extends GetxController {
  final RxBool dataCollection = true.obs;
  final RxBool analyticsTracking = false.obs;
  final RxBool crashReporting = true.obs;
  final RxBool personalizedAds = false.obs;
  final RxBool locationTracking = false.obs;
  final RxBool contactsAccess = false.obs;
  final RxBool cameraAccess = false.obs;
  final RxBool microphoneAccess = false.obs;
  final RxBool storageAccess = true.obs;
  final RxBool biometricData = false.obs;
  final RxBool shareUsageData = false.obs;
  final RxBool thirdPartyIntegration = false.obs;
  final RxBool cookieConsent = true.obs;
  final RxString dataRetentionPeriod = '2 years'.obs;
  final RxBool autoDeleteData = false.obs;
  final RxBool offlineMode = false.obs;
  final RxBool photoLibraryAccess = false.obs;

  final List<String> dataRetentionOptions = [
    '6 months',
    '1 year',
    '2 years',
    '5 years',
    'Never delete',
  ];

  @override
  void onInit() {
    super.onInit();
    _loadPrivacySettings();
  }

  void _loadPrivacySettings() {
    final storage = StorageService.to;
    dataCollection.value = storage.getBool('data_collection') ?? true;
    analyticsTracking.value = storage.getBool('analytics_tracking') ?? false;
    crashReporting.value = storage.getBool('crash_reporting') ?? true;
    personalizedAds.value = storage.getBool('personalized_ads') ?? false;
    locationTracking.value = storage.getBool('location_tracking') ?? false;
    contactsAccess.value = storage.getBool('contacts_access') ?? false;
    cameraAccess.value = storage.getBool('camera_access') ?? false;
    microphoneAccess.value = storage.getBool('microphone_access') ?? false;
    storageAccess.value = storage.getBool('storage_access') ?? true;
    biometricData.value = storage.getBool('biometric_data') ?? false;
    shareUsageData.value = storage.getBool('share_usage_data') ?? false;
    thirdPartyIntegration.value =
        storage.getBool('third_party_integration') ?? false;
    cookieConsent.value = storage.getBool('cookie_consent') ?? true;
    dataRetentionPeriod.value =
        storage.getString('data_retention_period').isEmpty
            ? '2 years'
            : storage.getString('data_retention_period');
    autoDeleteData.value = storage.getBool('auto_delete_data') ?? false;
    offlineMode.value = storage.getBool('offline_mode') ?? false;
    photoLibraryAccess.value = storage.getBool('photo_library_access') ?? false;
  }

  void toggleDataCollection() {
    dataCollection.value = !dataCollection.value;
    StorageService.to.setBool('data_collection', dataCollection.value);
    _showPrivacyFeedback('Data Collection', dataCollection.value);
  }

  void toggleAnalyticsTracking() {
    analyticsTracking.value = !analyticsTracking.value;
    StorageService.to.setBool('analytics_tracking', analyticsTracking.value);
    _showPrivacyFeedback('Analytics Tracking', analyticsTracking.value);
  }

  void toggleCrashReporting() {
    crashReporting.value = !crashReporting.value;
    StorageService.to.setBool('crash_reporting', crashReporting.value);
    _showPrivacyFeedback('Crash Reporting', crashReporting.value);
  }

  void togglePersonalizedAds() {
    personalizedAds.value = !personalizedAds.value;
    StorageService.to.setBool('personalized_ads', personalizedAds.value);
    _showPrivacyFeedback('Personalized Ads', personalizedAds.value);
  }

  void toggleLocationTracking() {
    if (!locationTracking.value) {
      _showLocationPermissionDialog();
    } else {
      locationTracking.value = false;
      StorageService.to.setBool('location_tracking', false);
      _showPrivacyFeedback('Location Tracking', false);
    }
  }

  void _showLocationPermissionDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Location Permission'),
        content: const Text(
          'This app would like to access your location to provide location-based features. Your location data will be processed according to our privacy policy.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Deny'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              locationTracking.value = true;
              StorageService.to.setBool('location_tracking', true);
              _showPrivacyFeedback('Location Tracking', true);
            },
            child: const Text('Allow'),
          ),
        ],
      ),
    );
  }

  void toggleContactsAccess() {
    contactsAccess.value = !contactsAccess.value;
    StorageService.to.setBool('contacts_access', contactsAccess.value);
    _showPrivacyFeedback('Contacts Access', contactsAccess.value);
  }

  void toggleCameraAccess() {
    cameraAccess.value = !cameraAccess.value;
    StorageService.to.setBool('camera_access', cameraAccess.value);
    _showPrivacyFeedback('Camera Access', cameraAccess.value);
  }

  void toggleMicrophoneAccess() {
    microphoneAccess.value = !microphoneAccess.value;
    StorageService.to.setBool('microphone_access', microphoneAccess.value);
    _showPrivacyFeedback('Microphone Access', microphoneAccess.value);
  }

  void toggleStorageAccess() {
    if (storageAccess.value) {
      Get.dialog(
        AlertDialog(
          title: const Text('Storage Access Required'),
          content: const Text(
            'Storage access is required for the app to function properly. Disabling this may cause the app to not work correctly.',
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Get.back();
                storageAccess.value = false;
                StorageService.to.setBool('storage_access', false);
                _showPrivacyFeedback('Storage Access', false);
              },
              child: const Text('Disable Anyway',
                  style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );
    } else {
      storageAccess.value = true;
      StorageService.to.setBool('storage_access', true);
      _showPrivacyFeedback('Storage Access', true);
    }
  }

  void toggleBiometricData() {
    biometricData.value = !biometricData.value;
    StorageService.to.setBool('biometric_data', biometricData.value);
    _showPrivacyFeedback('Biometric Data', biometricData.value);
  }

  void toggleShareUsageData() {
    shareUsageData.value = !shareUsageData.value;
    StorageService.to.setBool('share_usage_data', shareUsageData.value);
    _showPrivacyFeedback('Share Usage Data', shareUsageData.value);
  }

  void toggleThirdPartyIntegration() {
    thirdPartyIntegration.value = !thirdPartyIntegration.value;
    StorageService.to
        .setBool('third_party_integration', thirdPartyIntegration.value);
    _showPrivacyFeedback(
        'Third-Party Integration', thirdPartyIntegration.value);
  }

  void toggleCookieConsent() {
    cookieConsent.value = !cookieConsent.value;
    StorageService.to.setBool('cookie_consent', cookieConsent.value);
    _showPrivacyFeedback('Cookie Consent', cookieConsent.value);
  }

  void toggleAutoDeleteData() {
    autoDeleteData.value = !autoDeleteData.value;
    StorageService.to.setBool('auto_delete_data', autoDeleteData.value);
    _showPrivacyFeedback('Auto Delete Data', autoDeleteData.value);
  }

  void toggleOfflineMode() {
    offlineMode.value = !offlineMode.value;
    StorageService.to.setBool('offline_mode', offlineMode.value);
    _showPrivacyFeedback('Offline Mode', offlineMode.value);
  }

  void togglePhotoLibraryAccess() {
    photoLibraryAccess.value = !photoLibraryAccess.value;
    StorageService.to.setBool('photo_library_access', photoLibraryAccess.value);
    _showPrivacyFeedback('Photo Library Access', photoLibraryAccess.value);
  }

  void changeDataRetentionPeriod() {
    Get.dialog(
      AlertDialog(
        title: const Text('Data Retention Period'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: dataRetentionOptions.length,
            itemBuilder: (context, index) {
              final period = dataRetentionOptions[index];
              return Obx(() => RadioListTile<String>(
                    title: Text(period),
                    value: period,
                    groupValue: dataRetentionPeriod.value,
                    onChanged: (value) {
                      if (value != null) {
                        dataRetentionPeriod.value = value;
                        StorageService.to
                            .setString('data_retention_period', value);
                        Get.back();
                        Get.snackbar(
                          'Data Retention',
                          'Data retention period set to $value',
                          snackPosition: SnackPosition.BOTTOM,
                          duration: const Duration(seconds: 2),
                        );
                      }
                    },
                    activeColor: Theme.of(Get.context!).colorScheme.primary,
                  ));
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyFeedback(String setting, bool enabled) {
    Get.snackbar(
      setting,
      enabled ? 'Enabled' : 'Disabled',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void viewPrivacyPolicy() {
    Get.toNamed(AppRoutes.privacyPolicy);
  }

  void requestDataDeletion() {
    Get.dialog(
      AlertDialog(
        title: const Text('Request Data Deletion'),
        content: const Text(
          'This will request the deletion of all your personal data from our servers. This action cannot be undone and may take up to 30 days to complete.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _simulateDataDeletionRequest();
            },
            child: const Text('Request Deletion',
                style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _simulateDataDeletionRequest() {
    Get.snackbar(
      'Data Deletion Request',
      'Your data deletion request has been submitted. You will receive a confirmation email shortly.',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 4),
    );
  }

  void downloadPrivacyReport() {
    Get.dialog(
      AlertDialog(
        title: const Text('Privacy Report'),
        content: const Text(
          'Generate a comprehensive report of your privacy settings and data usage. This report will include all permissions, data collection settings, and retention policies.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _generatePrivacyReport();
            },
            child: const Text('Generate Report'),
          ),
        ],
      ),
    );
  }

  void _generatePrivacyReport() {
    Get.snackbar(
      'Generating Report',
      'Creating your privacy report...',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
      showProgressIndicator: true,
    );

    Future.delayed(const Duration(seconds: 4), () {
      Get.snackbar(
        'Report Ready',
        'Your privacy report has been generated and is ready for download',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    });
  }

  void resetAllPrivacySettings() {
    Get.dialog(
      AlertDialog(
        title: const Text('Reset Privacy Settings'),
        content: const Text(
          'This will reset all privacy settings to their default values. Are you sure you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _resetToDefaults();
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _resetToDefaults() {
    dataCollection.value = true;
    analyticsTracking.value = false;
    crashReporting.value = true;
    personalizedAds.value = false;
    locationTracking.value = false;
    contactsAccess.value = false;
    cameraAccess.value = false;
    microphoneAccess.value = false;
    storageAccess.value = true;
    biometricData.value = false;
    shareUsageData.value = false;
    thirdPartyIntegration.value = false;
    cookieConsent.value = true;
    dataRetentionPeriod.value = '2 years';
    autoDeleteData.value = false;
    offlineMode.value = false;
    photoLibraryAccess.value = false;

    // Save to storage
    final storage = StorageService.to;
    storage.setBool('data_collection', true);
    storage.setBool('analytics_tracking', false);
    storage.setBool('crash_reporting', true);
    storage.setBool('personalized_ads', false);
    storage.setBool('location_tracking', false);
    storage.setBool('contacts_access', false);
    storage.setBool('camera_access', false);
    storage.setBool('microphone_access', false);
    storage.setBool('storage_access', true);
    storage.setBool('biometric_data', false);
    storage.setBool('share_usage_data', false);
    storage.setBool('third_party_integration', false);
    storage.setBool('cookie_consent', true);
    storage.setString('data_retention_period', '2 years');
    storage.setBool('auto_delete_data', false);
    storage.setBool('offline_mode', false);
    storage.setBool('photo_library_access', false);

    Get.snackbar(
      'Privacy Settings Reset',
      'All privacy settings have been reset to default values',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }
}
